"""
Sistema de cadastro de rostos para o reconhecimento facial
"""

import cv2
import os
import json
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from face_recognizer import FaceR<PERSON>ognizer
from face_detector import FaceDetector


class FaceRegistration:
    """Sistema para cadastro e gerenciamento de rostos conhecidos"""
    
    def __init__(self, data_folder: str = "face_data"):
        """
        Inicializa o sistema de cadastro
        
        Args:
            data_folder: Pasta para armazenar dados dos rostos
        """
        self.data_folder = data_folder
        self.images_folder = os.path.join(data_folder, "images")
        self.metadata_file = os.path.join(data_folder, "metadata.json")
        
        # Cria pastas se não existirem
        os.makedirs(self.images_folder, exist_ok=True)
        
        self.face_recognizer = FaceRecognizer()
        self.face_detector = FaceDetector()
        self.metadata = self.load_metadata()
    
    def load_metadata(self) -> Dict:
        """
        Carrega metadados dos rostos cadastrados
        
        Returns:
            Dicionário com metadados
        """
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Erro ao carregar metadados: {e}")
        
        return {"people": {}, "last_updated": None}
    
    def save_metadata(self) -> None:
        """Salva metadados no arquivo"""
        self.metadata["last_updated"] = datetime.now().isoformat()
        
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Erro ao salvar metadados: {e}")
    
    def register_person_interactive(self) -> bool:
        """
        Registra uma pessoa de forma interativa usando a webcam
        
        Returns:
            True se o registro foi bem-sucedido
        """
        print("\n=== Cadastro de Nova Pessoa ===")
        
        # Coleta informações da pessoa
        name = input("Nome completo: ").strip()
        if not name:
            print("Nome não pode estar vazio")
            return False
        
        if name in self.metadata["people"]:
            overwrite = input(f"Pessoa '{name}' já existe. Sobrescrever? (s/n): ").lower()
            if overwrite != 's':
                return False
        
        # Informações adicionais (opcionais)
        age = input("Idade (opcional): ").strip()
        department = input("Departamento/Setor (opcional): ").strip()
        notes = input("Observações (opcional): ").strip()
        
        # Inicia captura de vídeo
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Erro: Não foi possível abrir a câmera")
            return False
        
        print("\nPosicione-se na frente da câmera")
        print("Pressione ESPAÇO para capturar uma foto")
        print("Pressione 'q' para cancelar")
        
        captured_images = []
        target_images = 5  # Número de imagens a capturar
        
        try:
            while len(captured_images) < target_images:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Espelha a imagem
                frame = cv2.flip(frame, 1)
                
                # Detecta rostos
                faces = self.face_detector.detect_faces_dlib(frame)
                
                # Desenha caixas dos rostos
                display_frame = self.face_detector.draw_face_boxes(frame, faces)
                
                # Adiciona instruções na tela
                cv2.putText(display_frame, f"Fotos capturadas: {len(captured_images)}/{target_images}", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                cv2.putText(display_frame, "ESPACO: Capturar | Q: Cancelar", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # Feedback visual
                if len(faces) == 1:
                    cv2.putText(display_frame, "Rosto detectado - Pronto para capturar", 
                               (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                elif len(faces) == 0:
                    cv2.putText(display_frame, "Nenhum rosto detectado", 
                               (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                else:
                    cv2.putText(display_frame, "Multiplos rostos - Posicione-se sozinho", 
                               (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
                
                cv2.imshow('Cadastro de Pessoa', display_frame)
                
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord(' '):  # Espaço para capturar
                    if len(faces) == 1:
                        # Salva a imagem
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"{name}_{timestamp}_{len(captured_images)}.jpg"
                        filepath = os.path.join(self.images_folder, filename)
                        
                        cv2.imwrite(filepath, frame)
                        captured_images.append(filepath)
                        
                        print(f"Foto {len(captured_images)}/{target_images} capturada")
                        
                        # Feedback visual
                        cv2.rectangle(display_frame, (0, 0), (display_frame.shape[1], display_frame.shape[0]), 
                                     (0, 255, 0), 10)
                        cv2.imshow('Cadastro de Pessoa', display_frame)
                        cv2.waitKey(200)  # Mostra feedback por 200ms
                    else:
                        print("Posicione-se sozinho na frente da câmera")
                
                elif key == ord('q'):
                    print("Cadastro cancelado")
                    cap.release()
                    cv2.destroyAllWindows()
                    # Remove imagens capturadas
                    for img_path in captured_images:
                        if os.path.exists(img_path):
                            os.remove(img_path)
                    return False
            
        except KeyboardInterrupt:
            print("\nCadastro interrompido")
            cap.release()
            cv2.destroyAllWindows()
            return False
        
        cap.release()
        cv2.destroyAllWindows()
        
        # Processa as imagens capturadas
        print(f"\nProcessando {len(captured_images)} imagens...")
        
        encodings_added = 0
        for i, image_path in enumerate(captured_images):
            if self.face_recognizer.add_person(f"{name}_{i}", image_path):
                encodings_added += 1
        
        if encodings_added == 0:
            print("Erro: Nenhum rosto válido encontrado nas imagens")
            # Remove imagens
            for img_path in captured_images:
                if os.path.exists(img_path):
                    os.remove(img_path)
            return False
        
        # Salva metadados
        person_data = {
            "name": name,
            "images": captured_images,
            "encodings_count": encodings_added,
            "registration_date": datetime.now().isoformat(),
            "age": age if age else None,
            "department": department if department else None,
            "notes": notes if notes else None
        }
        
        self.metadata["people"][name] = person_data
        self.save_metadata()
        
        # Salva encodings
        self.face_recognizer.save_encodings()
        
        print(f"\nPessoa '{name}' cadastrada com sucesso!")
        print(f"Imagens processadas: {encodings_added}/{len(captured_images)}")
        
        return True
    
    def register_person_from_folder(self, name: str, folder_path: str, 
                                   age: str = "", department: str = "", 
                                   notes: str = "") -> bool:
        """
        Registra uma pessoa a partir de imagens em uma pasta
        
        Args:
            name: Nome da pessoa
            folder_path: Caminho para a pasta com imagens
            age: Idade (opcional)
            department: Departamento (opcional)
            notes: Observações (opcional)
            
        Returns:
            True se o registro foi bem-sucedido
        """
        if not os.path.exists(folder_path):
            print(f"Erro: Pasta não encontrada: {folder_path}")
            return False
        
        # Encontra imagens na pasta
        supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
        image_files = []
        
        for filename in os.listdir(folder_path):
            if filename.lower().endswith(supported_formats):
                image_files.append(os.path.join(folder_path, filename))
        
        if not image_files:
            print("Nenhuma imagem válida encontrada na pasta")
            return False
        
        print(f"Encontradas {len(image_files)} imagens para processar")
        
        # Copia imagens para a pasta de dados
        copied_images = []
        for i, src_path in enumerate(image_files):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{name}_{timestamp}_{i}.jpg"
            dst_path = os.path.join(self.images_folder, filename)
            
            # Copia e redimensiona se necessário
            img = cv2.imread(src_path)
            if img is not None:
                # Redimensiona se muito grande
                height, width = img.shape[:2]
                if width > 1024 or height > 1024:
                    scale = min(1024/width, 1024/height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    img = cv2.resize(img, (new_width, new_height))
                
                cv2.imwrite(dst_path, img)
                copied_images.append(dst_path)
        
        # Adiciona ao reconhecedor
        encodings_added = 0
        for i, image_path in enumerate(copied_images):
            if self.face_recognizer.add_person(f"{name}_{i}", image_path):
                encodings_added += 1
        
        if encodings_added == 0:
            print("Erro: Nenhum rosto válido encontrado nas imagens")
            # Remove imagens copiadas
            for img_path in copied_images:
                if os.path.exists(img_path):
                    os.remove(img_path)
            return False
        
        # Salva metadados
        person_data = {
            "name": name,
            "images": copied_images,
            "encodings_count": encodings_added,
            "registration_date": datetime.now().isoformat(),
            "age": age if age else None,
            "department": department if department else None,
            "notes": notes if notes else None
        }
        
        self.metadata["people"][name] = person_data
        self.save_metadata()
        self.face_recognizer.save_encodings()
        
        print(f"Pessoa '{name}' cadastrada com sucesso!")
        print(f"Imagens processadas: {encodings_added}/{len(copied_images)}")
        
        return True
    
    def remove_person(self, name: str) -> bool:
        """
        Remove uma pessoa do sistema
        
        Args:
            name: Nome da pessoa a remover
            
        Returns:
            True se removido com sucesso
        """
        if name not in self.metadata["people"]:
            print(f"Pessoa '{name}' não encontrada")
            return False
        
        # Remove do reconhecedor
        self.face_recognizer.remove_person(name)
        
        # Remove imagens
        person_data = self.metadata["people"][name]
        for image_path in person_data.get("images", []):
            if os.path.exists(image_path):
                os.remove(image_path)
        
        # Remove dos metadados
        del self.metadata["people"][name]
        self.save_metadata()
        self.face_recognizer.save_encodings()
        
        print(f"Pessoa '{name}' removida com sucesso")
        return True
    
    def list_registered_people(self) -> None:
        """Lista todas as pessoas cadastradas"""
        if not self.metadata["people"]:
            print("Nenhuma pessoa cadastrada")
            return
        
        print(f"\n=== Pessoas Cadastradas ({len(self.metadata['people'])}) ===")
        
        for name, data in self.metadata["people"].items():
            print(f"\nNome: {name}")
            print(f"  Cadastrado em: {data.get('registration_date', 'N/A')}")
            print(f"  Imagens: {data.get('encodings_count', 0)}")
            
            if data.get('age'):
                print(f"  Idade: {data['age']}")
            if data.get('department'):
                print(f"  Departamento: {data['department']}")
            if data.get('notes'):
                print(f"  Observações: {data['notes']}")
    
    def get_person_info(self, name: str) -> Optional[Dict]:
        """
        Obtém informações de uma pessoa específica
        
        Args:
            name: Nome da pessoa
            
        Returns:
            Dicionário com informações da pessoa ou None se não encontrada
        """
        return self.metadata["people"].get(name)


if __name__ == "__main__":
    # Exemplo de uso
    registration = FaceRegistration()
    
    print("Sistema de Cadastro de Rostos")
    print("1. Cadastrar pessoa (webcam)")
    print("2. Listar pessoas cadastradas")
    print("3. Sair")
    
    while True:
        choice = input("\nEscolha uma opção: ").strip()
        
        if choice == "1":
            registration.register_person_interactive()
        elif choice == "2":
            registration.list_registered_people()
        elif choice == "3":
            break
        else:
            print("Opção inválida")
