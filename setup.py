"""
Script de configuração e instalação do SARA IA
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """Verifica se a versão do Python é compatível"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Erro: Python 3.7 ou superior é necessário")
        print(f"Versão atual: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
    return True


def install_dependencies():
    """Instala as dependências necessárias"""
    print("\n📦 Instalando dependências...")
    
    try:
        # Atualiza pip primeiro
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Instala dependências do requirements.txt
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependências instaladas com sucesso!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return False


def check_camera():
    """Verifica se a câmera está disponível"""
    print("\n📹 Verificando câmera...")
    
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                print("✅ Câmera detectada e funcionando")
                return True
            else:
                print("⚠️  Câmera detectada mas não conseguiu capturar imagem")
                return False
        else:
            print("❌ Nenhuma câmera detectada")
            return False
            
    except ImportError:
        print("❌ OpenCV não instalado - execute a instalação de dependências primeiro")
        return False
    except Exception as e:
        print(f"❌ Erro ao verificar câmera: {e}")
        return False


def create_directories():
    """Cria diretórios necessários"""
    print("\n📁 Criando diretórios...")
    
    directories = [
        "face_data",
        "face_data/images",
        "logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Criado: {directory}")
        else:
            print(f"📁 Já existe: {directory}")


def test_imports():
    """Testa se todas as bibliotecas podem ser importadas"""
    print("\n🧪 Testando importações...")
    
    required_modules = [
        ("cv2", "OpenCV"),
        ("face_recognition", "face_recognition"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("pickle", "pickle (built-in)")
    ]
    
    all_ok = True
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
        except ImportError as e:
            print(f"❌ {name} - ERRO: {e}")
            all_ok = False
    
    return all_ok


def run_basic_test():
    """Executa teste básico do sistema"""
    print("\n🔬 Executando teste básico...")
    
    try:
        from face_detector import FaceDetector
        from face_recognizer import FaceRecognizer
        
        # Testa detector
        detector = FaceDetector()
        print("✅ FaceDetector inicializado")
        
        # Testa reconhecedor
        recognizer = FaceRecognizer()
        print("✅ FaceRecognizer inicializado")
        
        # Testa imagem sintética
        import numpy as np
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        faces = detector.detect_faces_opencv(test_image)
        print("✅ Detecção facial funcionando")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste básico: {e}")
        return False


def show_system_info():
    """Mostra informações do sistema"""
    print("\n💻 Informações do Sistema:")
    print(f"Sistema Operacional: {platform.system()} {platform.release()}")
    print(f"Arquitetura: {platform.machine()}")
    print(f"Python: {sys.version}")
    print(f"Diretório atual: {os.getcwd()}")


def main():
    """Função principal de configuração"""
    print("=" * 60)
    print("    SARA IA - Setup e Configuração")
    print("    Sistema de Reconhecimento Facial")
    print("=" * 60)
    
    show_system_info()
    
    # Verifica versão do Python
    if not check_python_version():
        return False
    
    # Cria diretórios
    create_directories()
    
    # Pergunta se deve instalar dependências
    install_deps = input("\n📦 Deseja instalar/atualizar dependências? (s/n): ").lower()
    if install_deps == 's':
        if not install_dependencies():
            print("❌ Falha na instalação de dependências")
            return False
    
    # Testa importações
    if not test_imports():
        print("\n❌ Algumas bibliotecas não estão disponíveis")
        print("Execute: pip install -r requirements.txt")
        return False
    
    # Verifica câmera
    check_camera()
    
    # Executa teste básico
    if not run_basic_test():
        print("❌ Falha no teste básico do sistema")
        return False
    
    print("\n" + "=" * 60)
    print("✅ CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!")
    print("=" * 60)
    print("\n🚀 Para iniciar o sistema, execute:")
    print("   python sara_ia_main.py")
    print("\n📚 Para executar testes:")
    print("   python test_sara_ia.py")
    print("\n📖 Consulte o README.md para mais informações")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Configuração falhou. Verifique os erros acima.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Configuração cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado durante configuração: {e}")
        sys.exit(1)
