"""
Testes unitários para o sistema SARA IA
"""

import unittest
import os
import tempfile
import shutil
import numpy as np
import cv2
from unittest.mock import patch, MagicMock

# Importa os módulos do sistema
from face_detector import FaceDetector
from face_recognizer import FaceRecognizer
from face_registration import FaceRegistration


class TestFaceDetector(unittest.TestCase):
    """Testes para o módulo de detecção facial"""
    
    def setUp(self):
        """Configuração inicial dos testes"""
        self.detector = FaceDetector()
        
        # Cria uma imagem de teste simples
        self.test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        # Adiciona um retângulo branco simulando um rosto
        cv2.rectangle(self.test_image, (200, 150), (400, 350), (255, 255, 255), -1)
    
    def test_detector_initialization(self):
        """Testa a inicialização do detector"""
        self.assertIsNotNone(self.detector.face_cascade)
    
    def test_detect_faces_opencv_with_empty_image(self):
        """Testa detecção com imagem vazia"""
        empty_image = np.zeros((100, 100, 3), dtype=np.uint8)
        faces = self.detector.detect_faces_opencv(empty_image)
        self.assertIsInstance(faces, list)
    
    def test_draw_face_boxes(self):
        """Testa desenho de caixas nos rostos"""
        faces = [(100, 100, 50, 50)]
        labels = ["Teste"]
        
        result = self.detector.draw_face_boxes(self.test_image, faces, labels)
        
        # Verifica se a imagem foi modificada
        self.assertIsInstance(result, np.ndarray)
        self.assertEqual(result.shape, self.test_image.shape)
    
    def test_get_face_encodings_with_no_faces(self):
        """Testa extração de encodings sem rostos"""
        empty_image = np.zeros((100, 100, 3), dtype=np.uint8)
        encodings = self.detector.get_face_encodings(empty_image)
        self.assertEqual(len(encodings), 0)


class TestFaceRecognizer(unittest.TestCase):
    """Testes para o módulo de reconhecimento facial"""
    
    def setUp(self):
        """Configuração inicial dos testes"""
        # Cria diretório temporário para testes
        self.temp_dir = tempfile.mkdtemp()
        self.original_encodings_file = "face_encodings.pkl"
        
        # Muda o arquivo de encodings para o diretório temporário
        self.recognizer = FaceRecognizer()
        self.recognizer.encodings_file = os.path.join(self.temp_dir, "test_encodings.pkl")
    
    def tearDown(self):
        """Limpeza após os testes"""
        shutil.rmtree(self.temp_dir)
    
    def test_recognizer_initialization(self):
        """Testa inicialização do reconhecedor"""
        self.assertEqual(self.recognizer.tolerance, 0.6)
        self.assertEqual(len(self.recognizer.known_face_encodings), 0)
        self.assertEqual(len(self.recognizer.known_face_names), 0)
    
    def test_add_person_with_invalid_path(self):
        """Testa adição de pessoa com caminho inválido"""
        result = self.recognizer.add_person("Teste", "caminho_inexistente.jpg")
        self.assertFalse(result)
    
    def test_remove_person_not_found(self):
        """Testa remoção de pessoa não encontrada"""
        result = self.recognizer.remove_person("Pessoa Inexistente")
        self.assertFalse(result)
    
    def test_list_known_people_empty(self):
        """Testa listagem de pessoas quando vazia"""
        people = self.recognizer.list_known_people()
        self.assertEqual(len(people), 0)
    
    def test_get_statistics_empty(self):
        """Testa estatísticas com sistema vazio"""
        stats = self.recognizer.get_statistics()
        
        self.assertEqual(stats['total_encodings'], 0)
        self.assertEqual(stats['unique_people'], 0)
        self.assertEqual(stats['encodings_per_person'], {})
    
    def test_save_and_load_encodings(self):
        """Testa salvamento e carregamento de encodings"""
        # Adiciona dados de teste
        self.recognizer.known_face_names = ["Teste1", "Teste2"]
        self.recognizer.known_face_encodings = [
            np.array([1, 2, 3]), 
            np.array([4, 5, 6])
        ]
        
        # Salva
        self.recognizer.save_encodings()
        self.assertTrue(os.path.exists(self.recognizer.encodings_file))
        
        # Cria novo reconhecedor e carrega
        new_recognizer = FaceRecognizer()
        new_recognizer.encodings_file = self.recognizer.encodings_file
        result = new_recognizer.load_encodings()
        
        self.assertTrue(result)
        self.assertEqual(len(new_recognizer.known_face_names), 2)
        self.assertEqual(new_recognizer.known_face_names, ["Teste1", "Teste2"])


class TestFaceRegistration(unittest.TestCase):
    """Testes para o módulo de cadastro"""
    
    def setUp(self):
        """Configuração inicial dos testes"""
        self.temp_dir = tempfile.mkdtemp()
        self.registration = FaceRegistration(self.temp_dir)
    
    def tearDown(self):
        """Limpeza após os testes"""
        shutil.rmtree(self.temp_dir)
    
    def test_registration_initialization(self):
        """Testa inicialização do sistema de cadastro"""
        self.assertTrue(os.path.exists(self.registration.data_folder))
        self.assertTrue(os.path.exists(self.registration.images_folder))
        self.assertIsInstance(self.registration.metadata, dict)
    
    def test_load_metadata_empty(self):
        """Testa carregamento de metadados vazios"""
        metadata = self.registration.load_metadata()
        
        self.assertIn("people", metadata)
        self.assertIn("last_updated", metadata)
        self.assertEqual(len(metadata["people"]), 0)
    
    def test_save_and_load_metadata(self):
        """Testa salvamento e carregamento de metadados"""
        # Adiciona dados de teste
        self.registration.metadata["people"]["Teste"] = {
            "name": "Teste",
            "registration_date": "2024-01-01"
        }
        
        # Salva
        self.registration.save_metadata()
        self.assertTrue(os.path.exists(self.registration.metadata_file))
        
        # Carrega em nova instância
        new_registration = FaceRegistration(self.temp_dir)
        self.assertIn("Teste", new_registration.metadata["people"])
    
    def test_register_person_from_invalid_folder(self):
        """Testa cadastro de pasta inexistente"""
        result = self.registration.register_person_from_folder(
            "Teste", "pasta_inexistente"
        )
        self.assertFalse(result)
    
    def test_remove_person_not_found(self):
        """Testa remoção de pessoa não encontrada"""
        result = self.registration.remove_person("Pessoa Inexistente")
        self.assertFalse(result)
    
    def test_get_person_info_not_found(self):
        """Testa obtenção de informações de pessoa não encontrada"""
        info = self.registration.get_person_info("Pessoa Inexistente")
        self.assertIsNone(info)
    
    def test_list_registered_people_empty(self):
        """Testa listagem de pessoas quando vazia"""
        # Captura a saída para verificar se não há erro
        import io
        import sys
        
        captured_output = io.StringIO()
        sys.stdout = captured_output
        
        self.registration.list_registered_people()
        
        sys.stdout = sys.__stdout__
        output = captured_output.getvalue()
        
        self.assertIn("Nenhuma pessoa cadastrada", output)


class TestIntegration(unittest.TestCase):
    """Testes de integração do sistema"""
    
    def setUp(self):
        """Configuração inicial dos testes de integração"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Cria uma imagem de teste
        self.test_image_path = os.path.join(self.temp_dir, "test_face.jpg")
        test_image = np.ones((200, 200, 3), dtype=np.uint8) * 128
        cv2.imwrite(self.test_image_path, test_image)
    
    def tearDown(self):
        """Limpeza após os testes de integração"""
        shutil.rmtree(self.temp_dir)
    
    def test_full_workflow_simulation(self):
        """Testa um fluxo completo simulado do sistema"""
        # Inicializa componentes
        registration = FaceRegistration(self.temp_dir)
        recognizer = FaceRecognizer()
        recognizer.encodings_file = os.path.join(self.temp_dir, "encodings.pkl")
        
        # Verifica estado inicial
        self.assertEqual(len(recognizer.list_known_people()), 0)
        
        # Simula salvamento de encodings
        recognizer.save_encodings()
        self.assertTrue(os.path.exists(recognizer.encodings_file))
        
        # Verifica metadados
        registration.save_metadata()
        self.assertTrue(os.path.exists(registration.metadata_file))


def create_test_image(width=200, height=200, color=(128, 128, 128)):
    """
    Cria uma imagem de teste
    
    Args:
        width: Largura da imagem
        height: Altura da imagem
        color: Cor da imagem (B, G, R)
    
    Returns:
        Imagem numpy array
    """
    return np.full((height, width, 3), color, dtype=np.uint8)


def run_performance_test():
    """Executa teste básico de performance"""
    print("\n=== Teste de Performance ===")
    
    import time
    
    # Teste de detecção
    detector = FaceDetector()
    test_image = create_test_image(640, 480)
    
    start_time = time.time()
    for _ in range(10):
        faces = detector.detect_faces_opencv(test_image)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"Tempo médio de detecção OpenCV: {avg_time:.4f}s")
    
    # Teste de reconhecimento
    recognizer = FaceRecognizer()
    
    start_time = time.time()
    for _ in range(10):
        results = recognizer.recognize_faces(test_image)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 10
    print(f"Tempo médio de reconhecimento: {avg_time:.4f}s")


if __name__ == "__main__":
    print("=== Executando Testes do SARA IA ===")
    
    # Executa testes unitários
    unittest.main(verbosity=2, exit=False)
    
    # Executa teste de performance
    try:
        run_performance_test()
    except Exception as e:
        print(f"Erro no teste de performance: {e}")
    
    print("\n=== Testes Concluídos ===")
