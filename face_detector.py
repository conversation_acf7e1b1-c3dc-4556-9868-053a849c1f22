"""
Módulo de detecção facial usando OpenCV e face_recognition
"""

import cv2
import face_recognition
import numpy as np
from typing import List, Tuple, Optional
import os


class FaceDetector:
    """Classe para detecção de rostos em imagens e vídeos"""
    
    def __init__(self):
        """Inicializa o detector facial"""
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    def detect_faces_opencv(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        Detecta rostos usando OpenCV Haar Cascades
        
        Args:
            image: Imagem em formato numpy array (BGR)
            
        Returns:
            Lista de tuplas (x, y, w, h) com as coordenadas dos rostos detectados
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30)
        )
        return [(x, y, w, h) for (x, y, w, h) in faces]
    
    def detect_faces_dlib(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        Detecta rostos usando face_recognition (dlib)
        
        Args:
            image: Imagem em formato numpy array (RGB)
            
        Returns:
            Lista de tuplas (x, y, w, h) com as coordenadas dos rostos detectados
        """
        # Converte BGR para RGB se necessário
        if len(image.shape) == 3 and image.shape[2] == 3:
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            rgb_image = image
            
        face_locations = face_recognition.face_locations(rgb_image)
        
        # Converte formato (top, right, bottom, left) para (x, y, w, h)
        faces = []
        for (top, right, bottom, left) in face_locations:
            x, y = left, top
            w, h = right - left, bottom - top
            faces.append((x, y, w, h))
            
        return faces
    
    def get_face_encodings(self, image: np.ndarray) -> List[np.ndarray]:
        """
        Extrai encodings dos rostos detectados na imagem
        
        Args:
            image: Imagem em formato numpy array (BGR)
            
        Returns:
            Lista de encodings dos rostos encontrados
        """
        # Converte BGR para RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Encontra localizações dos rostos
        face_locations = face_recognition.face_locations(rgb_image)
        
        # Extrai encodings
        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        
        return face_encodings
    
    def draw_face_boxes(self, image: np.ndarray, faces: List[Tuple[int, int, int, int]], 
                       labels: Optional[List[str]] = None) -> np.ndarray:
        """
        Desenha caixas ao redor dos rostos detectados
        
        Args:
            image: Imagem original
            faces: Lista de coordenadas dos rostos (x, y, w, h)
            labels: Lista opcional de rótulos para cada rosto
            
        Returns:
            Imagem com as caixas desenhadas
        """
        result_image = image.copy()
        
        for i, (x, y, w, h) in enumerate(faces):
            # Desenha retângulo
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Adiciona label se fornecido
            if labels and i < len(labels):
                label = labels[i]
                cv2.putText(result_image, label, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
        
        return result_image
    
    def detect_faces_in_video(self, video_path: str, output_path: Optional[str] = None) -> None:
        """
        Detecta rostos em um vídeo e opcionalmente salva o resultado
        
        Args:
            video_path: Caminho para o arquivo de vídeo
            output_path: Caminho opcional para salvar o vídeo processado
        """
        cap = cv2.VideoCapture(video_path)
        
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Detecta rostos
            faces = self.detect_faces_opencv(frame)
            
            # Desenha caixas
            frame_with_faces = self.draw_face_boxes(frame, faces)
            
            # Salva frame se necessário
            if output_path:
                out.write(frame_with_faces)
            
            # Mostra frame
            cv2.imshow('Face Detection', frame_with_faces)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        if output_path:
            out.release()
        cv2.destroyAllWindows()


if __name__ == "__main__":
    # Exemplo de uso
    detector = FaceDetector()
    
    # Teste com webcam
    cap = cv2.VideoCapture(0)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        faces = detector.detect_faces_opencv(frame)
        frame_with_faces = detector.draw_face_boxes(frame, faces)
        
        cv2.imshow('Face Detection Test', frame_with_faces)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
