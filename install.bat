@echo off
echo ============================================
echo    SARA IA - Instalacao Automatica
echo    Sistema de Reconhecimento Facial
echo ============================================
echo.

REM Verifica se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Por favor, instale Python 3.7+ de https://python.org
    pause
    exit /b 1
)

echo Python encontrado!
python --version

echo.
echo Atualizando pip...
python -m pip install --upgrade pip

echo.
echo Instalando dependencias...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERRO: Falha na instalacao das dependencias!
    echo Tente executar manualmente: pip install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo Executando configuracao...
python setup.py

echo.
echo ============================================
echo    Instalacao concluida!
echo ============================================
echo.
echo Para iniciar o sistema, execute:
echo    python sara_ia_main.py
echo.
pause
