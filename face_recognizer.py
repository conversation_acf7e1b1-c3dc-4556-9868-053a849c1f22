"""
Módulo de reconhecimento facial usando face_recognition
"""

import face_recognition
import cv2
import numpy as np
import pickle
import os
from typing import List, Dict, Tuple, Optional
from face_detector import FaceDetector


class FaceRecognizer:
    """Classe para reconhecimento de rostos conhecidos"""
    
    def __init__(self, tolerance: float = 0.6):
        """
        Inicializa o reconhecedor facial
        
        Args:
            tolerance: Tolerância para comparação de rostos (menor = mais restritivo)
        """
        self.tolerance = tolerance
        self.known_face_encodings = []
        self.known_face_names = []
        self.face_detector = FaceDetector()
        self.encodings_file = "face_encodings.pkl"
        
        # Carrega encodings salvos se existirem
        self.load_encodings()
    
    def add_person(self, name: str, image_path: str) -> bool:
        """
        Adiciona uma nova pessoa ao sistema de reconhecimento
        
        Args:
            name: Nome da pessoa
            image_path: Caminho para a imagem da pessoa
            
        Returns:
            True se a pessoa foi adicionada com sucesso, False caso contrário
        """
        if not os.path.exists(image_path):
            print(f"Erro: Imagem não encontrada em {image_path}")
            return False
        
        # Carrega a imagem
        image = cv2.imread(image_path)
        if image is None:
            print(f"Erro: Não foi possível carregar a imagem {image_path}")
            return False
        
        # Converte BGR para RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Encontra encodings na imagem
        face_encodings = face_recognition.face_encodings(rgb_image)
        
        if len(face_encodings) == 0:
            print(f"Erro: Nenhum rosto encontrado na imagem {image_path}")
            return False
        
        if len(face_encodings) > 1:
            print(f"Aviso: Múltiplos rostos encontrados em {image_path}. Usando o primeiro.")
        
        # Adiciona o primeiro encoding encontrado
        face_encoding = face_encodings[0]
        self.known_face_encodings.append(face_encoding)
        self.known_face_names.append(name)
        
        print(f"Pessoa '{name}' adicionada com sucesso!")
        return True
    
    def add_person_from_multiple_images(self, name: str, image_folder: str) -> bool:
        """
        Adiciona uma pessoa usando múltiplas imagens para melhor precisão
        
        Args:
            name: Nome da pessoa
            image_folder: Pasta contendo imagens da pessoa
            
        Returns:
            True se pelo menos uma imagem foi processada com sucesso
        """
        if not os.path.exists(image_folder):
            print(f"Erro: Pasta não encontrada: {image_folder}")
            return False
        
        encodings_added = 0
        supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
        
        for filename in os.listdir(image_folder):
            if filename.lower().endswith(supported_formats):
                image_path = os.path.join(image_folder, filename)
                if self.add_person(f"{name}_{encodings_added}", image_path):
                    encodings_added += 1
        
        if encodings_added > 0:
            print(f"Adicionadas {encodings_added} imagens para '{name}'")
            return True
        else:
            print(f"Nenhuma imagem válida encontrada para '{name}'")
            return False
    
    def recognize_faces(self, image: np.ndarray) -> List[Tuple[str, float, Tuple[int, int, int, int]]]:
        """
        Reconhece rostos em uma imagem
        
        Args:
            image: Imagem em formato numpy array (BGR)
            
        Returns:
            Lista de tuplas (nome, confiança, (x, y, w, h)) para cada rosto reconhecido
        """
        if len(self.known_face_encodings) == 0:
            return []
        
        # Converte BGR para RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Encontra localizações e encodings dos rostos na imagem
        face_locations = face_recognition.face_locations(rgb_image)
        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        
        results = []
        
        for face_encoding, face_location in zip(face_encodings, face_locations):
            # Compara com rostos conhecidos
            matches = face_recognition.compare_faces(
                self.known_face_encodings, face_encoding, tolerance=self.tolerance
            )
            
            # Calcula distâncias
            face_distances = face_recognition.face_distance(
                self.known_face_encodings, face_encoding
            )
            
            name = "Desconhecido"
            confidence = 0.0
            
            if True in matches:
                best_match_index = np.argmin(face_distances)
                if matches[best_match_index]:
                    name = self.known_face_names[best_match_index]
                    confidence = 1 - face_distances[best_match_index]
            
            # Converte coordenadas (top, right, bottom, left) para (x, y, w, h)
            top, right, bottom, left = face_location
            x, y, w, h = left, top, right - left, bottom - top
            
            results.append((name, confidence, (x, y, w, h)))
        
        return results
    
    def save_encodings(self) -> None:
        """Salva os encodings conhecidos em arquivo"""
        data = {
            'encodings': self.known_face_encodings,
            'names': self.known_face_names
        }
        
        with open(self.encodings_file, 'wb') as f:
            pickle.dump(data, f)
        
        print(f"Encodings salvos em {self.encodings_file}")
    
    def load_encodings(self) -> bool:
        """
        Carrega encodings salvos do arquivo
        
        Returns:
            True se os encodings foram carregados com sucesso
        """
        if not os.path.exists(self.encodings_file):
            return False
        
        try:
            with open(self.encodings_file, 'rb') as f:
                data = pickle.load(f)
            
            self.known_face_encodings = data['encodings']
            self.known_face_names = data['names']
            
            print(f"Carregados {len(self.known_face_names)} rostos conhecidos")
            return True
            
        except Exception as e:
            print(f"Erro ao carregar encodings: {e}")
            return False
    
    def remove_person(self, name: str) -> bool:
        """
        Remove uma pessoa do sistema
        
        Args:
            name: Nome da pessoa a ser removida
            
        Returns:
            True se a pessoa foi removida com sucesso
        """
        indices_to_remove = []
        
        for i, known_name in enumerate(self.known_face_names):
            if known_name == name:
                indices_to_remove.append(i)
        
        if not indices_to_remove:
            print(f"Pessoa '{name}' não encontrada")
            return False
        
        # Remove em ordem reversa para não afetar os índices
        for i in reversed(indices_to_remove):
            del self.known_face_encodings[i]
            del self.known_face_names[i]
        
        print(f"Removidas {len(indices_to_remove)} entradas para '{name}'")
        return True
    
    def list_known_people(self) -> List[str]:
        """
        Lista todas as pessoas conhecidas
        
        Returns:
            Lista com nomes únicos das pessoas conhecidas
        """
        return list(set(self.known_face_names))
    
    def get_statistics(self) -> Dict[str, int]:
        """
        Retorna estatísticas do sistema
        
        Returns:
            Dicionário com estatísticas
        """
        unique_names = self.list_known_people()
        name_counts = {}
        
        for name in self.known_face_names:
            name_counts[name] = name_counts.get(name, 0) + 1
        
        return {
            'total_encodings': len(self.known_face_encodings),
            'unique_people': len(unique_names),
            'encodings_per_person': name_counts
        }


if __name__ == "__main__":
    # Exemplo de uso
    recognizer = FaceRecognizer()
    
    # Teste básico
    print("Sistema de Reconhecimento Facial")
    print("Estatísticas:", recognizer.get_statistics())
    print("Pessoas conhecidas:", recognizer.list_known_people())
