"""
SARA IA - Sistema Avançado de Reconhecimento e Análise de Imagens Artificiais
Aplicação principal de reconhecimento facial
"""

import sys
import os
from typing import Optional
from face_recognizer import FaceRecognizer
from face_detector import FaceDetector
from video_capture import VideoCapture
from face_registration import FaceRegistration


class SaraIA:
    """Classe principal do sistema SARA IA"""
    
    def __init__(self):
        """Inicializa o sistema SARA IA"""
        self.face_recognizer = FaceRecognizer()
        self.face_detector = FaceDetector()
        self.video_capture = VideoCapture()
        self.registration = FaceRegistration()
        
        print("=" * 60)
        print("    SARA IA - Sistema de Reconhecimento Facial")
        print("    Desenvolvido em Python com OpenCV e face_recognition")
        print("=" * 60)
    
    def show_main_menu(self) -> None:
        """Exibe o menu principal"""
        print("\n" + "=" * 40)
        print("           MENU PRINCIPAL")
        print("=" * 40)
        print("1. 🎥 Reconhecimento em Tempo Real")
        print("2. 👤 Cadastrar Nova Pessoa")
        print("3. 📋 Gerenciar Pessoas Cadastradas")
        print("4. 📁 Processar Imagem/Vídeo")
        print("5. ⚙️  Configurações")
        print("6. 📊 Estatísticas do Sistema")
        print("7. ❓ Ajuda")
        print("0. 🚪 Sair")
        print("=" * 40)
    
    def show_management_menu(self) -> None:
        """Exibe o menu de gerenciamento"""
        print("\n" + "=" * 40)
        print("      GERENCIAR PESSOAS")
        print("=" * 40)
        print("1. 📋 Listar Pessoas Cadastradas")
        print("2. 👤 Ver Detalhes de uma Pessoa")
        print("3. 🗑️  Remover Pessoa")
        print("4. 📁 Cadastrar de Pasta")
        print("5. 💾 Salvar Dados")
        print("6. 🔄 Recarregar Dados")
        print("0. ⬅️  Voltar ao Menu Principal")
        print("=" * 40)
    
    def show_settings_menu(self) -> None:
        """Exibe o menu de configurações"""
        print("\n" + "=" * 40)
        print("        CONFIGURAÇÕES")
        print("=" * 40)
        print("1. 🎯 Ajustar Tolerância de Reconhecimento")
        print("2. 📹 Configurar Câmera")
        print("3. 📂 Configurar Pastas")
        print("4. 🔄 Resetar Sistema")
        print("0. ⬅️  Voltar ao Menu Principal")
        print("=" * 40)
    
    def real_time_recognition(self) -> None:
        """Inicia reconhecimento em tempo real"""
        print("\n🎥 Iniciando reconhecimento em tempo real...")
        print("Pressione 'q' na janela de vídeo para sair")
        
        try:
            self.video_capture.run_recognition_loop()
        except Exception as e:
            print(f"Erro durante reconhecimento: {e}")
    
    def register_new_person(self) -> None:
        """Cadastra uma nova pessoa"""
        print("\n👤 Cadastro de Nova Pessoa")
        print("Você será guiado através do processo de cadastro")
        
        try:
            success = self.registration.register_person_interactive()
            if success:
                print("✅ Pessoa cadastrada com sucesso!")
            else:
                print("❌ Falha no cadastro")
        except Exception as e:
            print(f"Erro durante cadastro: {e}")
    
    def manage_people(self) -> None:
        """Menu de gerenciamento de pessoas"""
        while True:
            self.show_management_menu()
            choice = input("\nEscolha uma opção: ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                self.registration.list_registered_people()
            elif choice == "2":
                name = input("Digite o nome da pessoa: ").strip()
                if name:
                    info = self.registration.get_person_info(name)
                    if info:
                        print(f"\n📋 Informações de {name}:")
                        for key, value in info.items():
                            if value:
                                print(f"  {key}: {value}")
                    else:
                        print(f"❌ Pessoa '{name}' não encontrada")
            elif choice == "3":
                name = input("Digite o nome da pessoa a remover: ").strip()
                if name:
                    confirm = input(f"Tem certeza que deseja remover '{name}'? (s/n): ").lower()
                    if confirm == 's':
                        if self.registration.remove_person(name):
                            print("✅ Pessoa removida com sucesso!")
                        else:
                            print("❌ Falha ao remover pessoa")
            elif choice == "4":
                name = input("Nome da pessoa: ").strip()
                folder = input("Caminho da pasta com imagens: ").strip()
                if name and folder:
                    age = input("Idade (opcional): ").strip()
                    dept = input("Departamento (opcional): ").strip()
                    notes = input("Observações (opcional): ").strip()
                    
                    if self.registration.register_person_from_folder(name, folder, age, dept, notes):
                        print("✅ Pessoa cadastrada com sucesso!")
                    else:
                        print("❌ Falha no cadastro")
            elif choice == "5":
                self.face_recognizer.save_encodings()
                self.registration.save_metadata()
                print("✅ Dados salvos com sucesso!")
            elif choice == "6":
                self.face_recognizer.load_encodings()
                self.registration.metadata = self.registration.load_metadata()
                print("✅ Dados recarregados com sucesso!")
            else:
                print("❌ Opção inválida")
    
    def process_file(self) -> None:
        """Processa arquivo de imagem ou vídeo"""
        print("\n📁 Processamento de Arquivo")
        file_path = input("Digite o caminho do arquivo (imagem ou vídeo): ").strip()
        
        if not os.path.exists(file_path):
            print("❌ Arquivo não encontrado")
            return
        
        # Verifica se é imagem ou vídeo
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.wmv')
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in image_extensions:
            self.process_image(file_path)
        elif file_ext in video_extensions:
            self.process_video(file_path)
        else:
            print("❌ Formato de arquivo não suportado")
    
    def process_image(self, image_path: str) -> None:
        """Processa uma imagem"""
        import cv2
        
        print(f"📷 Processando imagem: {image_path}")
        
        image = cv2.imread(image_path)
        if image is None:
            print("❌ Erro ao carregar imagem")
            return
        
        # Reconhece rostos
        results = self.face_recognizer.recognize_faces(image)
        
        if not results:
            print("👤 Nenhum rosto detectado na imagem")
            return
        
        print(f"👥 Encontrados {len(results)} rosto(s):")
        for i, (name, confidence, (x, y, w, h)) in enumerate(results, 1):
            if name != "Desconhecido":
                print(f"  {i}. {name} (confiança: {confidence:.2f})")
            else:
                print(f"  {i}. Pessoa desconhecida")
        
        # Desenha resultados e mostra
        result_image = self.video_capture.draw_recognition_results(image, results)
        cv2.imshow('Resultado do Reconhecimento', result_image)
        
        print("Pressione qualquer tecla na janela da imagem para continuar...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        # Opção de salvar resultado
        save = input("Deseja salvar a imagem com os resultados? (s/n): ").lower()
        if save == 's':
            output_path = f"resultado_{os.path.basename(image_path)}"
            cv2.imwrite(output_path, result_image)
            print(f"✅ Resultado salvo em: {output_path}")
    
    def process_video(self, video_path: str) -> None:
        """Processa um vídeo"""
        print(f"🎬 Processando vídeo: {video_path}")
        
        save = input("Deseja salvar o vídeo processado? (s/n): ").lower()
        output_path = None
        
        if save == 's':
            output_path = f"resultado_{os.path.basename(video_path)}"
        
        try:
            self.video_capture.process_video_file(video_path, output_path)
            if output_path:
                print(f"✅ Vídeo processado salvo em: {output_path}")
        except Exception as e:
            print(f"❌ Erro ao processar vídeo: {e}")
    
    def show_settings(self) -> None:
        """Menu de configurações"""
        while True:
            self.show_settings_menu()
            choice = input("\nEscolha uma opção: ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                current = self.face_recognizer.tolerance
                print(f"Tolerância atual: {current}")
                print("(Valores menores = mais restritivo, valores maiores = mais permissivo)")
                
                try:
                    new_tolerance = float(input("Nova tolerância (0.1-1.0): "))
                    if 0.1 <= new_tolerance <= 1.0:
                        self.face_recognizer.tolerance = new_tolerance
                        print(f"✅ Tolerância alterada para {new_tolerance}")
                    else:
                        print("❌ Valor deve estar entre 0.1 e 1.0")
                except ValueError:
                    print("❌ Valor inválido")
            
            elif choice == "2":
                current = self.video_capture.camera_index
                print(f"Câmera atual: {current}")
                
                try:
                    new_camera = int(input("Novo índice da câmera: "))
                    self.video_capture.camera_index = new_camera
                    print(f"✅ Câmera alterada para índice {new_camera}")
                except ValueError:
                    print("❌ Valor inválido")
            
            elif choice == "3":
                print(f"Pasta de dados: {self.registration.data_folder}")
                print(f"Pasta de imagens: {self.registration.images_folder}")
                print("(Para alterar, edite o código ou reinicie o programa)")
            
            elif choice == "4":
                confirm = input("⚠️  ATENÇÃO: Isso removerá todos os dados! Continuar? (s/n): ").lower()
                if confirm == 's':
                    # Remove arquivos de dados
                    if os.path.exists(self.face_recognizer.encodings_file):
                        os.remove(self.face_recognizer.encodings_file)
                    if os.path.exists(self.registration.metadata_file):
                        os.remove(self.registration.metadata_file)
                    
                    # Reinicializa objetos
                    self.face_recognizer = FaceRecognizer()
                    self.registration = FaceRegistration()
                    
                    print("✅ Sistema resetado com sucesso!")
            else:
                print("❌ Opção inválida")
    
    def show_statistics(self) -> None:
        """Mostra estatísticas do sistema"""
        print("\n📊 Estatísticas do Sistema")
        print("=" * 40)
        
        stats = self.face_recognizer.get_statistics()
        print(f"Total de encodings: {stats['total_encodings']}")
        print(f"Pessoas únicas: {stats['unique_people']}")
        print(f"Tolerância atual: {self.face_recognizer.tolerance}")
        
        if stats['encodings_per_person']:
            print("\nEncodings por pessoa:")
            for name, count in stats['encodings_per_person'].items():
                print(f"  {name}: {count}")
        
        # Estatísticas de arquivos
        total_images = 0
        if os.path.exists(self.registration.images_folder):
            total_images = len([f for f in os.listdir(self.registration.images_folder) 
                              if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])
        
        print(f"\nImagens armazenadas: {total_images}")
        print(f"Pasta de dados: {self.registration.data_folder}")
    
    def show_help(self) -> None:
        """Mostra ajuda do sistema"""
        print("\n❓ Ajuda - SARA IA")
        print("=" * 50)
        print("🎥 Reconhecimento em Tempo Real:")
        print("   - Use sua webcam para reconhecer rostos em tempo real")
        print("   - Pressione 'c' para capturar um novo rosto")
        print("   - Pressione 'q' para sair")
        print()
        print("👤 Cadastro de Pessoas:")
        print("   - Capture múltiplas fotos para melhor precisão")
        print("   - Posicione-se bem iluminado e de frente para a câmera")
        print("   - Evite óculos escuros ou objetos cobrindo o rosto")
        print()
        print("⚙️ Configurações:")
        print("   - Tolerância: controla a sensibilidade do reconhecimento")
        print("   - Valores menores = mais restritivo")
        print("   - Valores maiores = mais permissivo")
        print()
        print("📁 Processamento de Arquivos:")
        print("   - Suporta imagens: JPG, PNG, BMP, TIFF")
        print("   - Suporta vídeos: MP4, AVI, MOV, MKV, WMV")
        print()
        print("💡 Dicas:")
        print("   - Mantenha boa iluminação")
        print("   - Evite sombras no rosto")
        print("   - Cadastre múltiplas fotos da mesma pessoa")
        print("   - Faça backup dos dados regularmente")
    
    def run(self) -> None:
        """Executa o loop principal da aplicação"""
        try:
            while True:
                self.show_main_menu()
                choice = input("\nEscolha uma opção: ").strip()
                
                if choice == "0":
                    print("\n👋 Obrigado por usar o SARA IA!")
                    break
                elif choice == "1":
                    self.real_time_recognition()
                elif choice == "2":
                    self.register_new_person()
                elif choice == "3":
                    self.manage_people()
                elif choice == "4":
                    self.process_file()
                elif choice == "5":
                    self.show_settings()
                elif choice == "6":
                    self.show_statistics()
                elif choice == "7":
                    self.show_help()
                else:
                    print("❌ Opção inválida. Tente novamente.")
                
                input("\nPressione Enter para continuar...")
        
        except KeyboardInterrupt:
            print("\n\n👋 Sistema encerrado pelo usuário. Até logo!")
        except Exception as e:
            print(f"\n❌ Erro inesperado: {e}")
            print("Por favor, reinicie o sistema.")


if __name__ == "__main__":
    # Verifica se as dependências estão instaladas
    try:
        import cv2
        import face_recognition
        import numpy as np
    except ImportError as e:
        print(f"❌ Erro: Dependência não encontrada: {e}")
        print("Execute: pip install -r requirements.txt")
        sys.exit(1)
    
    # Inicia o sistema
    sara = SaraIA()
    sara.run()
