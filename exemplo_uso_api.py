"""
Exemplo de uso da API do SARA IA
Demonstra como usar os módulos programaticamente
"""

import cv2
import numpy as np
import os
from face_detector import FaceDetector
from face_recognizer import <PERSON>Recognizer
from face_registration import FaceRegistration


def exemplo_deteccao_basica():
    """Exemplo básico de detecção facial"""
    print("=== Exemplo: Detecção Facial Básica ===")
    
    # Inicializa o detector
    detector = FaceDetector()
    
    # Carrega uma imagem (substitua pelo caminho da sua imagem)
    # image_path = "sua_imagem.jpg"
    # if os.path.exists(image_path):
    #     image = cv2.imread(image_path)
    #     
    #     # Detecta rostos
    #     faces = detector.detect_faces_dlib(image)
    #     print(f"Rostos detectados: {len(faces)}")
    #     
    #     # Desenha caixas nos rostos
    #     result = detector.draw_face_boxes(image, faces)
    #     
    #     # Mostra resultado
    #     cv2.imshow('Detecção Facial', result)
    #     cv2.waitKey(0)
    #     cv2.destroyAllWindows()
    # else:
    #     print("Imagem não encontrada. Crie uma imagem de teste primeiro.")
    
    # Exemplo com webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Erro: Não foi possível abrir a câmera")
        return
    
    print("Pressione 'q' para sair")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Detecta rostos
        faces = detector.detect_faces_opencv(frame)
        
        # Desenha caixas
        result = detector.draw_face_boxes(frame, faces)
        
        # Mostra resultado
        cv2.imshow('Detecção em Tempo Real', result)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()


def exemplo_reconhecimento_programatico():
    """Exemplo de reconhecimento facial programático"""
    print("\n=== Exemplo: Reconhecimento Programático ===")
    
    # Inicializa o reconhecedor
    recognizer = FaceRecognizer()
    
    # Exemplo de adição de pessoa (descomente e ajuste o caminho)
    # success = recognizer.add_person("João", "foto_joao.jpg")
    # if success:
    #     print("Pessoa adicionada com sucesso!")
    #     recognizer.save_encodings()
    
    # Carrega encodings existentes
    recognizer.load_encodings()
    
    print(f"Pessoas conhecidas: {recognizer.list_known_people()}")
    print(f"Estatísticas: {recognizer.get_statistics()}")
    
    # Exemplo de reconhecimento em tempo real
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Erro: Não foi possível abrir a câmera")
        return
    
    print("Reconhecimento em tempo real - Pressione 'q' para sair")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Reconhece rostos
        results = recognizer.recognize_faces(frame)
        
        # Desenha resultados
        for name, confidence, (x, y, w, h) in results:
            color = (0, 255, 0) if name != "Desconhecido" else (0, 0, 255)
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            label = f"{name}"
            if name != "Desconhecido":
                label += f" ({confidence:.2f})"
            
            cv2.putText(frame, label, (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        cv2.imshow('Reconhecimento Facial', frame)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()


def exemplo_cadastro_programatico():
    """Exemplo de cadastro programático"""
    print("\n=== Exemplo: Cadastro Programático ===")
    
    # Inicializa o sistema de cadastro
    registration = FaceRegistration("dados_exemplo")
    
    # Lista pessoas cadastradas
    registration.list_registered_people()
    
    # Exemplo de cadastro de pasta (descomente e ajuste)
    # success = registration.register_person_from_folder(
    #     name="Maria Silva",
    #     folder_path="fotos_maria/",
    #     age="28",
    #     department="Marketing",
    #     notes="Gerente de marketing"
    # )
    # 
    # if success:
    #     print("Pessoa cadastrada com sucesso!")
    
    # Mostra estatísticas
    print("\nEstatísticas do cadastro:")
    if registration.metadata["people"]:
        for name, data in registration.metadata["people"].items():
            print(f"- {name}: {data.get('encodings_count', 0)} encodings")


def exemplo_processamento_lote():
    """Exemplo de processamento em lote de imagens"""
    print("\n=== Exemplo: Processamento em Lote ===")
    
    recognizer = FaceRecognizer()
    recognizer.load_encodings()
    
    # Pasta com imagens para processar (ajuste o caminho)
    input_folder = "imagens_para_processar"
    output_folder = "resultados_processamento"
    
    if not os.path.exists(input_folder):
        print(f"Pasta {input_folder} não encontrada. Criando exemplo...")
        os.makedirs(input_folder, exist_ok=True)
        print(f"Adicione imagens na pasta {input_folder} e execute novamente")
        return
    
    os.makedirs(output_folder, exist_ok=True)
    
    # Formatos suportados
    supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
    
    # Processa cada imagem
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(supported_formats):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, f"resultado_{filename}")
            
            print(f"Processando: {filename}")
            
            # Carrega imagem
            image = cv2.imread(input_path)
            if image is None:
                continue
            
            # Reconhece rostos
            results = recognizer.recognize_faces(image)
            
            # Desenha resultados
            for name, confidence, (x, y, w, h) in results:
                color = (0, 255, 0) if name != "Desconhecido" else (0, 0, 255)
                cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
                
                label = f"{name}"
                if name != "Desconhecido":
                    label += f" ({confidence:.2f})"
                
                cv2.putText(image, label, (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Salva resultado
            cv2.imwrite(output_path, image)
            print(f"  - {len(results)} rosto(s) encontrado(s)")
    
    print(f"Processamento concluído. Resultados em: {output_folder}")


def exemplo_configuracao_avancada():
    """Exemplo de configurações avançadas"""
    print("\n=== Exemplo: Configurações Avançadas ===")
    
    # Reconhecedor com tolerância personalizada
    recognizer = FaceRecognizer(tolerance=0.5)  # Mais restritivo
    print(f"Tolerância configurada: {recognizer.tolerance}")
    
    # Detector com configurações personalizadas
    detector = FaceDetector()
    
    # Exemplo de detecção com parâmetros ajustados
    cap = cv2.VideoCapture(0)
    if cap.isOpened():
        # Configura resolução
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        ret, frame = cap.read()
        if ret:
            print(f"Resolução configurada: {frame.shape[1]}x{frame.shape[0]}")
        
        cap.release()
    
    # Exemplo de processamento otimizado
    print("Configurações de performance:")
    print("- Processe a cada N frames para melhor FPS")
    print("- Redimensione imagens grandes antes do processamento")
    print("- Use cache de resultados quando possível")


def menu_exemplos():
    """Menu interativo dos exemplos"""
    while True:
        print("\n" + "=" * 50)
        print("    SARA IA - Exemplos de Uso da API")
        print("=" * 50)
        print("1. Detecção Facial Básica")
        print("2. Reconhecimento Programático")
        print("3. Cadastro Programático")
        print("4. Processamento em Lote")
        print("5. Configurações Avançadas")
        print("0. Sair")
        print("=" * 50)
        
        choice = input("Escolha um exemplo: ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            exemplo_deteccao_basica()
        elif choice == "2":
            exemplo_reconhecimento_programatico()
        elif choice == "3":
            exemplo_cadastro_programatico()
        elif choice == "4":
            exemplo_processamento_lote()
        elif choice == "5":
            exemplo_configuracao_avancada()
        else:
            print("Opção inválida!")
        
        input("\nPressione Enter para continuar...")


if __name__ == "__main__":
    print("SARA IA - Exemplos de Uso da API")
    print("Este arquivo demonstra como usar os módulos programaticamente")
    
    try:
        menu_exemplos()
    except KeyboardInterrupt:
        print("\n\nExemplos encerrados pelo usuário.")
    except Exception as e:
        print(f"\nErro durante execução dos exemplos: {e}")
        print("Verifique se todas as dependências estão instaladas.")
