# SARA IA - Sistema Avançado de Reconhecimento e Análise de Imagens Artificiais

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![OpenCV](https://img.shields.io/badge/OpenCV-4.8+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 📋 Descrição

SARA IA é um sistema completo de reconhecimento facial desenvolvido em Python, utilizando OpenCV e a biblioteca face_recognition. O sistema oferece funcionalidades avançadas para detecção, reconhecimento e gerenciamento de rostos em tempo real.

## ✨ Funcionalidades

- 🎥 **Reconhecimento em Tempo Real**: Identifica rostos usando webcam
- 👤 **Cadastro Interativo**: Sistema guiado para cadastrar novas pessoas
- 📁 **Processamento de Arquivos**: Analisa imagens e vídeos
- 📊 **Gerenciamento Completo**: Interface para gerenciar pessoas cadastradas
- ⚙️ **Configurações Avançadas**: Ajuste de tolerância e parâmetros
- 💾 **Persistência de Dados**: Salva e carrega dados automaticamente
- 📈 **Estatísticas**: Relatórios detalhados do sistema

## 🚀 Instalação

### Pré-requisitos

- Python 3.7 ou superior
- Webcam (para reconhecimento em tempo real)
- Windows/Linux/macOS

### Passo a Passo

1. **Clone ou baixe os arquivos do projeto**

2. **Instale as dependências**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Execute o sistema**:
   ```bash
   python sara_ia_main.py
   ```

## 📦 Dependências

- `opencv-python==********` - Processamento de imagens e vídeo
- `face-recognition==1.3.0` - Reconhecimento facial
- `numpy==1.24.3` - Operações matemáticas
- `Pillow==10.0.1` - Manipulação de imagens
- `dlib==19.24.2` - Algoritmos de visão computacional
- `cmake==3.27.7` - Compilação do dlib

## 🎯 Como Usar

### 1. Primeiro Uso

1. Execute `python sara_ia_main.py`
2. Escolha a opção "2 - Cadastrar Nova Pessoa"
3. Siga as instruções para cadastrar seu primeiro rosto
4. Use a opção "1 - Reconhecimento em Tempo Real" para testar

### 2. Reconhecimento em Tempo Real

- Selecione a opção 1 no menu principal
- Posicione-se na frente da câmera
- O sistema identificará rostos conhecidos automaticamente
- Pressione 'q' para sair

### 3. Cadastro de Pessoas

**Método Interativo (Recomendado)**:
- Opção 2 no menu principal
- Siga as instruções na tela
- Capture 5 fotos diferentes para melhor precisão

**Método por Pasta**:
- Menu "Gerenciar Pessoas" → "Cadastrar de Pasta"
- Forneça uma pasta com múltiplas imagens da pessoa

### 4. Processamento de Arquivos

- Opção 4 no menu principal
- Suporta imagens: JPG, PNG, BMP, TIFF
- Suporta vídeos: MP4, AVI, MOV, MKV, WMV

## 🏗️ Arquitetura do Sistema

```
sara_ia/
├── sara_ia_main.py          # Aplicação principal
├── face_detector.py         # Módulo de detecção facial
├── face_recognizer.py       # Módulo de reconhecimento
├── face_registration.py     # Sistema de cadastro
├── video_capture.py         # Interface de vídeo
├── test_sara_ia.py         # Testes unitários
├── requirements.txt         # Dependências
├── README.md               # Documentação
└── face_data/              # Dados do sistema (criado automaticamente)
    ├── images/             # Imagens cadastradas
    ├── metadata.json       # Metadados das pessoas
    └── face_encodings.pkl  # Encodings faciais
```

## 🔧 Configurações

### Tolerância de Reconhecimento

- **Padrão**: 0.6
- **Mais Restritivo**: 0.4-0.5 (menos falsos positivos)
- **Mais Permissivo**: 0.7-0.8 (mais reconhecimentos)

### Performance

- O sistema processa a cada 3 frames para melhor performance
- Redimensiona imagens grandes automaticamente
- Usa cache de resultados para fluidez

## 🧪 Testes

Execute os testes unitários:

```bash
python test_sara_ia.py
```

Os testes incluem:
- Testes de detecção facial
- Testes de reconhecimento
- Testes de cadastro
- Testes de integração
- Testes de performance

## 📊 Estrutura dos Dados

### Metadados (metadata.json)
```json
{
  "people": {
    "João Silva": {
      "name": "João Silva",
      "images": ["path1.jpg", "path2.jpg"],
      "encodings_count": 5,
      "registration_date": "2024-01-01T10:00:00",
      "age": "30",
      "department": "TI",
      "notes": "Funcionário do setor de desenvolvimento"
    }
  },
  "last_updated": "2024-01-01T10:00:00"
}
```

### Encodings (face_encodings.pkl)
Arquivo binário contendo:
- Lista de encodings faciais (arrays numpy)
- Lista de nomes correspondentes

## 🎨 Interface do Usuário

### Menu Principal
```
==========================================
           MENU PRINCIPAL
==========================================
1. 🎥 Reconhecimento em Tempo Real
2. 👤 Cadastrar Nova Pessoa
3. 📋 Gerenciar Pessoas Cadastradas
4. 📁 Processar Imagem/Vídeo
5. ⚙️  Configurações
6. 📊 Estatísticas do Sistema
7. ❓ Ajuda
0. 🚪 Sair
==========================================
```

### Controles do Vídeo em Tempo Real
- **Q**: Sair
- **C**: Capturar rosto para cadastro
- **S**: Salvar encodings
- **R**: Recarregar encodings
- **L**: Listar pessoas conhecidas

## 🔍 Algoritmos Utilizados

### Detecção Facial
1. **Haar Cascades (OpenCV)**: Rápido, adequado para tempo real
2. **HOG + SVM (dlib)**: Mais preciso, usado via face_recognition

### Reconhecimento Facial
1. **Deep Learning**: Rede neural convolucional para extração de features
2. **Comparação Euclidiana**: Calcula distâncias entre encodings
3. **Threshold Adaptativo**: Tolerância configurável

## 🚨 Solução de Problemas

### Erro: "Câmera não encontrada"
- Verifique se a webcam está conectada
- Tente alterar o índice da câmera nas configurações
- Teste com outros aplicativos de câmera

### Erro: "Nenhum rosto detectado"
- Melhore a iluminação
- Posicione-se de frente para a câmera
- Remova óculos escuros ou objetos que cobrem o rosto

### Performance Lenta
- Reduza a resolução da câmera
- Aumente o valor de `process_every_n_frames`
- Feche outros aplicativos que usam a câmera

### Reconhecimento Impreciso
- Cadastre mais imagens da mesma pessoa
- Ajuste a tolerância nas configurações
- Use imagens com boa qualidade e iluminação

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 👨‍💻 Autor

Desenvolvido com ❤️ usando Python, OpenCV e face_recognition.

## 🙏 Agradecimentos

- [OpenCV](https://opencv.org/) - Biblioteca de visão computacional
- [face_recognition](https://github.com/ageitgey/face_recognition) - Biblioteca de reconhecimento facial
- [dlib](http://dlib.net/) - Algoritmos de machine learning

## 📞 Suporte

Para suporte e dúvidas:
- Abra uma issue no repositório
- Consulte a documentação completa
- Execute os testes para verificar a instalação

---

**SARA IA** - Sistema Avançado de Reconhecimento e Análise de Imagens Artificiais
