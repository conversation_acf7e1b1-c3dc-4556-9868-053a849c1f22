"""
Interface de captura de vídeo em tempo real para reconhecimento facial
"""

import cv2
import numpy as np
from typing import Optional, Callable, Dict, Any
import time
from face_recognizer import FaceRecognizer
from face_detector import FaceDetector


class VideoCapture:
    """Classe para captura e processamento de vídeo em tempo real"""
    
    def __init__(self, camera_index: int = 0):
        """
        Inicializa a captura de vídeo
        
        Args:
            camera_index: Índi<PERSON> da câmera (0 para câmera padrão)
        """
        self.camera_index = camera_index
        self.cap = None
        self.is_running = False
        self.face_recognizer = FaceRecognizer()
        self.face_detector = FaceDetector()
        
        # Configurações de performance
        self.process_every_n_frames = 3  # Processa a cada N frames para melhor performance
        self.frame_count = 0
        self.last_recognition_results = []
        
        # Estatísticas
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
    
    def start_capture(self) -> bool:
        """
        Inicia a captura de vídeo
        
        Returns:
            True se a captura foi iniciada com sucesso
        """
        self.cap = cv2.VideoCapture(self.camera_index)
        
        if not self.cap.isOpened():
            print(f"Erro: Não foi possível abrir a câmera {self.camera_index}")
            return False
        
        # Configura resolução (opcional)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        self.is_running = True
        print("Captura de vídeo iniciada")
        return True
    
    def stop_capture(self) -> None:
        """Para a captura de vídeo"""
        self.is_running = False
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("Captura de vídeo parada")
    
    def calculate_fps(self) -> None:
        """Calcula FPS atual"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def draw_info_overlay(self, frame: np.ndarray) -> np.ndarray:
        """
        Desenha informações na tela
        
        Args:
            frame: Frame atual
            
        Returns:
            Frame com informações desenhadas
        """
        height, width = frame.shape[:2]
        
        # Desenha FPS
        cv2.putText(frame, f"FPS: {self.current_fps}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Desenha número de pessoas conhecidas
        known_people = len(self.face_recognizer.list_known_people())
        cv2.putText(frame, f"Pessoas conhecidas: {known_people}", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Desenha instruções
        instructions = [
            "Pressione 'q' para sair",
            "Pressione 'c' para capturar rosto",
            "Pressione 's' para salvar encodings"
        ]
        
        for i, instruction in enumerate(instructions):
            y_pos = height - 90 + (i * 25)
            cv2.putText(frame, instruction, (10, y_pos),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return frame
    
    def draw_recognition_results(self, frame: np.ndarray, 
                               results: list) -> np.ndarray:
        """
        Desenha resultados do reconhecimento no frame
        
        Args:
            frame: Frame atual
            results: Lista de resultados do reconhecimento
            
        Returns:
            Frame com resultados desenhados
        """
        for name, confidence, (x, y, w, h) in results:
            # Cor da caixa baseada na confiança
            if name == "Desconhecido":
                color = (0, 0, 255)  # Vermelho
            elif confidence > 0.7:
                color = (0, 255, 0)  # Verde
            elif confidence > 0.5:
                color = (0, 255, 255)  # Amarelo
            else:
                color = (255, 0, 0)  # Azul
            
            # Desenha retângulo
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # Prepara texto
            if name != "Desconhecido":
                label = f"{name} ({confidence:.2f})"
            else:
                label = name
            
            # Desenha fundo do texto
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(frame, (x, y - 25), (x + label_size[0], y), color, -1)
            
            # Desenha texto
            cv2.putText(frame, label, (x, y - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return frame
    
    def capture_face_for_training(self, frame: np.ndarray, name: str) -> bool:
        """
        Captura um rosto do frame atual para treinamento
        
        Args:
            frame: Frame atual
            name: Nome da pessoa
            
        Returns:
            True se o rosto foi capturado com sucesso
        """
        # Detecta rostos no frame
        faces = self.face_detector.detect_faces_dlib(frame)
        
        if len(faces) == 0:
            print("Nenhum rosto detectado para captura")
            return False
        
        if len(faces) > 1:
            print("Múltiplos rostos detectados. Posicione-se sozinho na frente da câmera")
            return False
        
        # Salva o frame temporariamente
        temp_filename = f"temp_capture_{name}.jpg"
        cv2.imwrite(temp_filename, frame)
        
        # Adiciona a pessoa ao reconhecedor
        success = self.face_recognizer.add_person(name, temp_filename)
        
        # Remove arquivo temporário
        import os
        if os.path.exists(temp_filename):
            os.remove(temp_filename)
        
        return success
    
    def run_recognition_loop(self) -> None:
        """Loop principal de reconhecimento facial"""
        if not self.start_capture():
            return
        
        print("\n=== Sistema de Reconhecimento Facial ===")
        print("Instruções:")
        print("- Pressione 'q' para sair")
        print("- Pressione 'c' para capturar seu rosto (será solicitado o nome)")
        print("- Pressione 's' para salvar encodings")
        print("- Pressione 'r' para recarregar encodings")
        print("- Pressione 'l' para listar pessoas conhecidas")
        
        try:
            while self.is_running:
                ret, frame = self.cap.read()
                if not ret:
                    print("Erro ao capturar frame")
                    break
                
                # Espelha a imagem horizontalmente para parecer um espelho
                frame = cv2.flip(frame, 1)
                
                # Calcula FPS
                self.calculate_fps()
                
                # Processa reconhecimento a cada N frames para melhor performance
                if self.frame_count % self.process_every_n_frames == 0:
                    self.last_recognition_results = self.face_recognizer.recognize_faces(frame)
                
                # Desenha resultados do reconhecimento
                frame = self.draw_recognition_results(frame, self.last_recognition_results)
                
                # Desenha overlay de informações
                frame = self.draw_info_overlay(frame)
                
                # Mostra o frame
                cv2.imshow('Reconhecimento Facial - SARA IA', frame)
                
                # Processa teclas pressionadas
                key = cv2.waitKey(1) & 0xFF
                
                if key == ord('q'):
                    break
                elif key == ord('c'):
                    name = input("\nDigite o nome da pessoa: ").strip()
                    if name:
                        print(f"Capturando rosto para '{name}'...")
                        if self.capture_face_for_training(frame, name):
                            print(f"Rosto de '{name}' capturado com sucesso!")
                        else:
                            print("Falha ao capturar rosto")
                    else:
                        print("Nome não pode estar vazio")
                elif key == ord('s'):
                    self.face_recognizer.save_encodings()
                elif key == ord('r'):
                    self.face_recognizer.load_encodings()
                    print("Encodings recarregados")
                elif key == ord('l'):
                    people = self.face_recognizer.list_known_people()
                    print(f"\nPessoas conhecidas ({len(people)}): {', '.join(people)}")
                
                self.frame_count += 1
                
        except KeyboardInterrupt:
            print("\nInterrompido pelo usuário")
        
        finally:
            self.stop_capture()
    
    def process_video_file(self, video_path: str, output_path: Optional[str] = None) -> None:
        """
        Processa um arquivo de vídeo para reconhecimento facial
        
        Args:
            video_path: Caminho para o arquivo de vídeo
            output_path: Caminho opcional para salvar o vídeo processado
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"Erro: Não foi possível abrir o vídeo {video_path}")
            return
        
        # Configurações do vídeo de saída
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_count = 0
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Processando vídeo: {total_frames} frames")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Reconhece rostos
            results = self.face_recognizer.recognize_faces(frame)
            
            # Desenha resultados
            frame = self.draw_recognition_results(frame, results)
            
            # Salva frame se necessário
            if output_path:
                out.write(frame)
            
            # Mostra progresso
            frame_count += 1
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"Progresso: {progress:.1f}%")
        
        cap.release()
        if output_path:
            out.release()
        
        print("Processamento do vídeo concluído")


if __name__ == "__main__":
    # Exemplo de uso
    video_capture = VideoCapture()
    video_capture.run_recognition_loop()
